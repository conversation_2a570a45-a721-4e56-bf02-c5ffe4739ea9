{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?fe77", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?b708", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?29d6", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?5d05", "uni-app:///node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?a54d", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?fef9"], "names": ["name", "mixins", "data", "show", "methods", "click", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4UAEN;AACP,KAAK;AACL;AACA,aAAa,0TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACyC/2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,eAuBA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-notice-bar/u-notice-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-notice-bar.vue?vue&type=template&id=4eff50a7&scoped=true&\"\nvar renderjs\nimport script from \"./u-notice-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-notice-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-notice-bar.vue?vue&type=style&index=0&id=4eff50a7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4eff50a7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=template&id=4eff50a7&scoped=true&\"", "var components\ntry {\n  components = {\n    uColumnNotice: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-column-notice/u-column-notice\" */ \"uview-ui/components/u-column-notice/u-column-notice.vue\"\n      )\n    },\n    uRowNotice: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-row-notice/u-row-notice\" */ \"uview-ui/components/u-row-notice/u-row-notice.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show\n    ? _vm.__get_style([\n        {\n          backgroundColor: _vm.bgColor,\n        },\n        _vm.$u.addStyle(_vm.customStyle),\n      ])\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t\tclass=\"u-notice-bar\"\n\t\tv-if=\"show\"\n\t\t:style=\"[{\n\t\t\tbackgroundColor: bgColor\n\t\t}, $u.addStyle(customStyle)]\"\n\t>\n\t\t<template v-if=\"direction === 'column' || (direction === 'row' && step)\">\n\t\t\t<u-column-notice\n\t\t\t\t:color=\"color\"\n\t\t\t\t:bgColor=\"bgColor\"\n\t\t\t\t:text=\"text\"\n\t\t\t\t:mode=\"mode\"\n\t\t\t\t:step=\"step\"\n\t\t\t\t:icon=\"icon\"\n\t\t\t\t:disable-touch=\"disableTouch\"\n\t\t\t\t:fontSize=\"fontSize\"\n\t\t\t\t:duration=\"duration\"\n\t\t\t\t@close=\"close\"\n\t\t\t\t@click=\"click\"\n\t\t\t></u-column-notice>\n\t\t</template>\n\t\t<template v-else>\n\t\t\t<u-row-notice\n\t\t\t\t:color=\"color\"\n\t\t\t\t:bgColor=\"bgColor\"\n\t\t\t\t:text=\"text\"\n\t\t\t\t:mode=\"mode\"\n\t\t\t\t:fontSize=\"fontSize\"\n\t\t\t\t:speed=\"speed\"\n\t\t\t\t:url=\"url\"\n\t\t\t\t:linkType=\"linkType\"\n\t\t\t\t:icon=\"icon\"\n\t\t\t\t@close=\"close\"\n\t\t\t\t@click=\"click\"\n\t\t\t></u-row-notice>\n\t\t</template>\n\t</view>\n</template>\n<script>\n\timport props from './props.js';\n\n\t/**\n\t * noticeBar 滚动通知\n\t * @description 该组件用于滚动通告场景，有多种模式可供选择\n\t * @tutorial https://www.uviewui.com/components/noticeBar.html\n\t * @property {Array | String}\ttext\t\t\t显示的内容，数组\n\t * @property {String}\t\t\tdirection\t\t通告滚动模式，row-横向滚动，column-竖向滚动 ( 默认 'row' )\n\t * @property {Boolean}\t\t\tstep\t\t\tdirection = row时，是否使用步进形式滚动  ( 默认 false )\n\t * @property {String}\t\t\ticon\t\t\t是否显示左侧的音量图标 ( 默认 'volume' )\n\t * @property {String}\t\t\tmode\t\t\t通告模式，link-显示右箭头，closable-显示右侧关闭图标\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色，各图标也会使用文字颜色 ( 默认 '#f9ae3d' )\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色 ( 默认 '#fdf6ec' )\n\t * @property {String | Number}\tspeed\t\t\t水平滚动时的滚动速度，即每秒滚动多少px(px)，这有利于控制文字无论多少时，都能有一个恒定的速度 ( 默认 80 )\n\t * @property {String | Number}\tfontSize\t\t字体大小 ( 默认 14 )\n\t * @property {String | Number}\tduration\t\t滚动一个周期的时间长，单位ms ( 默认 2000 )\n\t * @property {Boolean}\t\t\tdisableTouch\t是否禁止用手滑动切换 目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序（默认34） ( 默认 true )\n\t * @property {String}\t\t\turl\t\t\t\t跳转的页面路径\n\t * @property {String}\t\t\tlinkType\t\t页面跳转的类型 ( 默认 navigateTo )\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t * \n\t * @event {Function}\t\t\tclick\t\t\t点击通告文字触发\n\t * @event {Function}\t\t\tclose\t\t\t点击右侧关闭图标触发\n\t * @example <u-notice-bar :more-icon=\"true\" :list=\"list\"></u-notice-bar>\n\t */\n\texport default {\n\t\tname: \"u-notice-bar\",\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshow: true\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 点击通告栏\n\t\t\tclick(index) {\n\t\t\t\tthis.$emit('click', index)\n\t\t\t\tif (this.url && this.linkType) {\n\t\t\t\t\t// 此方法写在mixin中，另外跳转的url和linkType参数也在mixin的props中\n\t\t\t\t\tthis.openPage()\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 点击关闭按钮\n\t\t\tclose() {\n\t\t\t\tthis.show = false\n\t\t\t\tthis.$emit('close')\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-notice-bar {\n\t\toverflow: hidden;\n\t\tpadding: 9px 12px;\n\t\tflex: 1;\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=style&index=0&id=4eff50a7&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=style&index=0&id=4eff50a7&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755746291753\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}