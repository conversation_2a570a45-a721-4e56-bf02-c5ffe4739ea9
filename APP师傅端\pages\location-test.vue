<template>
  <view class="container">
    <view class="header">
      <text class="title">定位权限测试页面</text>
      <text class="subtitle">测试新的定位权限处理功能</text>
    </view>
    
    <view class="button-group">
      <button class="test-btn" @click="testCheckPermission">
        检查定位权限（显示弹窗）
      </button>
      
      <button class="test-btn" @click="testSilentCheck">
        静默检查定位权限
      </button>
      
      <button class="test-btn" @click="testShowModal">
        手动显示权限设置弹窗
      </button>
      
      <button class="test-btn" @click="testGetLocation">
        获取定位信息
      </button>
      
      <button class="test-btn" @click="testClearCache">
        清除定位缓存
      </button>
    </view>
    
    <view class="result-area">
      <text class="result-title">测试结果:</text>
      <scroll-view class="result-content" scroll-y>
        <text class="result-text">{{ resultText }}</text>
      </scroll-view>
    </view>
    
    <view class="info-area">
      <text class="info-title">功能说明:</text>
      <text class="info-text">
        1. 检查定位权限：会显示权限设置弹窗引导用户
        2. 静默检查：不显示弹窗，仅返回权限状态
        3. 手动显示弹窗：直接触发权限设置弹窗
        4. 获取定位：使用原有方法获取定位（现在会自动处理权限）
        5. 清除缓存：清除本地定位缓存
      </text>
    </view>
  </view>
</template>

<script>
import locationManager from '../utils/location-manager.js';

export default {
  data() {
    return {
      resultText: '点击按钮开始测试...\n'
    };
  },
  
  methods: {
    addResult(text) {
      const timestamp = new Date().toLocaleTimeString();
      this.resultText += `[${timestamp}] ${text}\n`;
    },
    
    async testCheckPermission() {
      this.addResult('开始检查定位权限（显示弹窗）...');
      
      try {
        const hasPermission = await locationManager.checkLocationPermission();
        this.addResult(`权限检查结果: ${hasPermission ? '有权限' : '无权限'}`);
      } catch (error) {
        this.addResult(`权限检查失败: ${error.message}`);
      }
    },
    
    async testSilentCheck() {
      this.addResult('开始静默检查定位权限...');
      
      try {
        const hasPermission = await locationManager.checkLocationPermission({ showModal: false });
        this.addResult(`静默检查结果: ${hasPermission ? '有权限' : '无权限'}`);
      } catch (error) {
        this.addResult(`静默检查失败: ${error.message}`);
      }
    },
    
    testShowModal() {
      this.addResult('手动显示权限设置弹窗...');
      locationManager.showLocationPermissionModal();
    },
    
    async testGetLocation() {
      this.addResult('开始获取定位信息...');
      
      try {
        const location = await locationManager.getLocation({ silent: false });
        this.addResult(`定位获取成功: ${location.address || '地址解析中...'}`);
        this.addResult(`经纬度: ${location.lng}, ${location.lat}`);
      } catch (error) {
        this.addResult(`定位获取失败: ${error.message}`);
      }
    },
    
    testClearCache() {
      this.addResult('清除定位缓存...');
      locationManager.clearCache();
      this.addResult('定位缓存已清除');
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 14px;
  color: #666;
  display: block;
}

.button-group {
  margin-bottom: 30px;
}

.test-btn {
  width: 100%;
  margin-bottom: 15px;
  padding: 15px;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
}

.test-btn:active {
  background-color: #0056cc;
}

.result-area {
  margin-bottom: 30px;
}

.result-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.result-content {
  height: 200px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
}

.result-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  white-space: pre-wrap;
}

.info-area {
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.info-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10px;
}

.info-text {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  display: block;
}
</style>
