# 定位管理器回退指南

## 快速回退步骤

如果新的定位权限功能出现问题，可以按照以下步骤快速回退到原始版本：

### 方法1: 使用备份文件回退
```bash
# 1. 备份当前版本（可选）
cp APP师傅端/utils/location-manager.js APP师傅端/utils/location-manager-v1.1.0.js

# 2. 恢复原始版本
cp APP师傅端/utils/location-manager-backup.js APP师傅端/utils/location-manager.js
```

### 方法2: 手动禁用新功能
如果只想临时禁用新功能，可以注释掉权限处理调用：

在 `_getLocationFromSystem()` 方法中，注释掉这一行：
```javascript
// this._handleLocationPermissionError(err);
```

### 方法3: 选择性回退
如果只想回退部分功能，可以：

1. **保留权限检查方法，禁用自动弹窗**：
   ```javascript
   // 在 _handleLocationPermissionError 方法开头添加
   return; // 临时禁用权限弹窗
   ```

2. **保留弹窗，禁用系统设置跳转**：
   ```javascript
   // 在 _openLocationSettings 方法开头添加
   return; // 临时禁用系统设置跳转
   ```

## 验证回退成功

回退后，验证以下功能是否正常：

1. **基本定位功能**：
   ```javascript
   const location = await locationManager.getLocation();
   console.log('定位结果:', location);
   ```

2. **缓存功能**：
   ```javascript
   // 第一次调用
   await locationManager.getLocation();
   // 第二次调用应该使用缓存
   await locationManager.getLocation();
   ```

3. **存储功能**：
   ```javascript
   const loginData = locationManager.getLocationForLogin();
   console.log('登录定位数据:', loginData);
   ```

## 常见问题排查

### 问题1: 回退后仍有权限弹窗
**原因**: 可能有其他地方调用了新的权限方法
**解决**: 搜索项目中是否有调用 `checkLocationPermission` 或 `showLocationPermissionModal`

### 问题2: 定位功能完全失效
**原因**: 文件复制可能有问题
**解决**: 
1. 检查文件内容是否正确
2. 重新导入 locationManager
3. 重启开发服务器

### 问题3: 缓存数据丢失
**原因**: 回退过程中可能清除了本地存储
**解决**: 
1. 重新获取定位信息
2. 检查本地存储是否正常

## 文件清理

如果确定不再需要新功能，可以删除以下文件：
- `APP师傅端/utils/location-test-example.js`
- `APP师傅端/utils/location-manager-README.md`
- `APP师傅端/pages/location-test.vue`
- `APP师傅端/utils/rollback-location-manager.md`（本文件）

## 重新启用新功能

如果回退后想重新启用新功能：
```bash
# 恢复新版本
cp APP师傅端/utils/location-manager-v1.1.0.js APP师傅端/utils/location-manager.js
```

## 联系支持

如果遇到无法解决的问题，请提供以下信息：
1. 具体的错误信息
2. 操作步骤
3. 设备和平台信息
4. 是否成功回退到原始版本
