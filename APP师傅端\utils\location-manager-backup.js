/**
 * 定位管理工具 - 原始版本备份
 * 统一管理定位获取，避免频繁调用
 * 备份时间: 2025-08-21
 * 备份说明: 这是添加权限处理功能前的原始版本
 */

class LocationManager {
  constructor() {
    this.isGettingLocation = false; // 是否正在获取定位
    this.locationCache = null; // 定位缓存
    this.cacheExpiry = 5 * 60 * 1000; // 缓存5分钟
    this.lastUpdateTime = 0; // 上次更新时间
    this.callbacks = []; // 等待定位的回调队列
  }

  /**
   * 获取定位信息
   * @param {Object} options 配置选项
   * @param {boolean} options.forceUpdate 是否强制更新
   * @param {boolean} options.silent 是否静默获取（不显示loading）
   * @returns {Promise} 定位信息
   */
  async getLocation(options = {}) {
    const { forceUpdate = false, silent = true } = options;
    const now = Date.now();

    // 检查缓存是否有效
    if (!forceUpdate && this.locationCache && (now - this.lastUpdateTime < this.cacheExpiry)) {
      console.log('使用缓存的定位信息:', this.locationCache);
      return Promise.resolve(this.locationCache);
    }

    // 如果正在获取定位，加入等待队列
    if (this.isGettingLocation) {
      console.log('定位获取中，加入等待队列');
      return new Promise((resolve, reject) => {
        this.callbacks.push({ resolve, reject });
      });
    }

    // 开始获取定位
    this.isGettingLocation = true;
    
    if (!silent) {
      uni.showLoading({ title: '获取定位中...' });
    }

    try {
      const locationData = await this._getLocationFromSystem();
      const addressData = await this._getAddressFromLocation(locationData);
      
      // 构造完整的定位信息
      const fullLocationData = {
        ...locationData,
        ...addressData,
        timestamp: now
      };

      // 保存到缓存和本地存储
      this.locationCache = fullLocationData;
      this.lastUpdateTime = now;
      this._saveToStorage(fullLocationData);

      console.log('定位获取成功:', fullLocationData);

      // 通知所有等待的回调
      this._notifyCallbacks(null, fullLocationData);

      return fullLocationData;
    } catch (error) {
      console.error('定位获取失败:', error);
      
      // 尝试使用本地存储的定位信息
      const cachedData = this._getFromStorage();
      if (cachedData) {
        console.log('使用本地存储的定位信息:', cachedData);
        this.locationCache = cachedData;
        this._notifyCallbacks(null, cachedData);
        return cachedData;
      }

      // 通知所有等待的回调
      this._notifyCallbacks(error, null);
      throw error;
    } finally {
      this.isGettingLocation = false;
      if (!silent) {
        uni.hideLoading();
      }
    }
  }

  /**
   * 从系统获取定位 - 原始版本
   */
  _getLocationFromSystem() {
    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: "gcj02",
        isHighAccuracy: true,
        accuracy: "best",
        timeout: 10000, // 10秒超时
        success: (res) => {
          resolve({
            lng: res.longitude,
            lat: res.latitude,
            accuracy: res.accuracy
          });
        },
        fail: (err) => {
          console.error('系统定位失败:', err);
          reject(new Error('定位失败，请检查定位权限'));
        }
      });
    });
  }

  /**
   * 根据经纬度获取地址信息
   */
  _getAddressFromLocation(locationData) {
    return new Promise((resolve) => {
      const { lng, lat } = locationData;
      
      // 使用高德地图逆地理编码
      uni.request({
        url: `https://restapi.amap.com/v3/geocode/regeo?key=2036e9b214b103fcb49c00a23de129e3&location=${lng},${lat}`,
        timeout: 8000, // 8秒超时
        success: (res) => {
          if (res.data && res.data.regeocode) {
            const regeocode = res.data.regeocode;
            const addressComponent = regeocode.addressComponent;
            const province = addressComponent.province;
            const city = typeof addressComponent.city === "string" ? addressComponent.city : province;
            
            const addressData = {
              address: regeocode.formatted_address || '',
              province: province || '',
              city: city || '',
              district: addressComponent.district || '',
              adcode: addressComponent.adcode || '',
              regeocode: regeocode
            };
            
            console.log('地址解析成功:', addressData.address);
            resolve(addressData);
          } else {
            console.warn('地址解析失败，返回空地址');
            resolve({ address: '', province: '', city: '', district: '', adcode: '', regeocode: null });
          }
        },
        fail: (err) => {
          console.error('地址解析请求失败:', err);
          resolve({ address: '', province: '', city: '', district: '', adcode: '', regeocode: null });
        }
      });
    });
  }

  /**
   * 保存定位信息到本地存储
   */
  _saveToStorage(locationData) {
    try {
      uni.setStorageSync("lng", locationData.lng || '');
      uni.setStorageSync("lat", locationData.lat || '');
      uni.setStorageSync("regeocode", locationData.regeocode || null);
      
      if (locationData.city && locationData.adcode) {
        uni.setStorageSync("city", {
          city_id: locationData.adcode,
          position: locationData.city
        });
      }
      
      // 保存完整的定位数据
      uni.setStorageSync("locationData", {
        ...locationData,
        cacheTime: Date.now()
      });
      
      console.log('定位信息已保存到本地存储');
    } catch (error) {
      console.error('保存定位信息到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储获取定位信息
   */
  _getFromStorage() {
    try {
      const locationData = uni.getStorageSync("locationData");
      if (locationData && locationData.lng && locationData.lat) {
        // 检查缓存是否过期
        const now = Date.now();
        if (now - locationData.cacheTime < this.cacheExpiry) {
          return locationData;
        }
      }
      return null;
    } catch (error) {
      console.error('从本地存储获取定位信息失败:', error);
      return null;
    }
  }

  /**
   * 通知所有等待的回调
   */
  _notifyCallbacks(error, data) {
    const callbacks = this.callbacks.slice();
    this.callbacks = [];
    
    callbacks.forEach(callback => {
      if (error) {
        callback.reject(error);
      } else {
        callback.resolve(data);
      }
    });
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.locationCache = null;
    this.lastUpdateTime = 0;
    console.log('定位缓存已清除');
  }

  /**
   * 获取简化的定位数据（用于登录接口）
   */
  getLocationForLogin() {
    const cachedData = this._getFromStorage() || this.locationCache;
    
    if (cachedData) {
      return {
        lng: cachedData.lng || '',
        lat: cachedData.lat || '',
        address: cachedData.address || ''
      };
    }

    // 如果没有缓存，尝试从旧的存储格式获取
    try {
      const lng = uni.getStorageSync('lng') || '';
      const lat = uni.getStorageSync('lat') || '';
      const regeocode = uni.getStorageSync('regeocode');
      const address = regeocode?.formatted_address || '';

      return { lng, lat, address };
    } catch (error) {
      console.error('获取登录定位数据失败:', error);
      return { lng: '', lat: '', address: '' };
    }
  }
}

// 创建单例实例
const locationManager = new LocationManager();

export default locationManager;
