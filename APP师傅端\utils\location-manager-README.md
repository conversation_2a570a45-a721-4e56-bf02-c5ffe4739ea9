# 定位管理器 - 权限处理功能说明

## 版本更新
- **v1.1.0** (2025-08-21): 添加定位权限失败弹窗和系统设置跳转功能

## 新增功能

### 1. 自动权限处理
当用户拒绝定位授权或未开启定位时，系统会自动显示弹窗提示用户前往设置。

### 2. 跨平台支持
- **APP端**: 显示确认弹窗，用户确认后直接跳转到系统设置
  - iOS: 跳转到应用设置页面 (`app-settings://`)
  - Android: 优先跳转到定位设置页面，失败时跳转到应用详情设置
- **微信小程序**: 显示提示弹窗，引导用户手动设置
- **其他平台**: 显示通用提示信息

### 3. 新增API方法

#### `checkLocationPermission(options)`
检查定位权限并可选择性显示引导弹窗
```javascript
// 检查权限并显示弹窗引导（默认行为）
const hasPermission = await locationManager.checkLocationPermission();

// 静默检查权限，不显示弹窗
const hasPermission = await locationManager.checkLocationPermission({ showModal: false });
```

#### `showLocationPermissionModal()`
手动触发定位权限设置弹窗
```javascript
locationManager.showLocationPermissionModal();
```

## 使用场景

### 场景1: 页面需要定位时
```javascript
async handleNeedLocation() {
  const hasPermission = await locationManager.checkLocationPermission();
  
  if (hasPermission) {
    // 有权限，获取定位
    const location = await locationManager.getLocation();
    // 使用定位信息...
  }
  // 没有权限时，用户会看到设置引导弹窗
}
```

### 场景2: 应用启动时静默检查
```javascript
async onAppLaunch() {
  // 静默检查，不打扰用户
  const hasPermission = await locationManager.checkLocationPermission({ showModal: false });
  
  if (hasPermission) {
    // 后台获取定位，提升用户体验
    await locationManager.getLocation({ silent: true });
  }
}
```

### 场景3: 用户主动请求定位权限
```javascript
onUserRequestLocation() {
  // 直接显示权限设置弹窗
  locationManager.showLocationPermissionModal();
}
```

## 弹窗文案

### APP端
- **标题**: "定位权限"
- **内容**: "获取定位失败，是否前往设置开启定位权限？"
- **按钮**: "去设置" / "取消"

### 微信小程序
- **标题**: "定位权限"
- **内容**: "获取定位失败，请在小程序设置中开启定位权限"
- **按钮**: "知道了"

### 其他平台
- **标题**: "获取定位失败"
- **内容**: "请检查手机是否开启定位功能"
- **按钮**: "知道了"

## 系统设置跳转

### iOS
- 跳转到应用的系统设置页面
- 用户可以在此开启定位权限

### Android
1. 优先跳转到系统定位设置页面 (`android.settings.LOCATION_SOURCE_SETTINGS`)
2. 如果失败，跳转到应用详情设置页面 (`Settings.ACTION_APPLICATION_DETAILS_SETTINGS`)
3. 用户可以在此开启定位权限或系统定位功能

## 错误处理
- 所有跳转失败都会显示友好的错误提示
- 不会影响应用的正常运行
- 提供降级处理方案

## 兼容性
- 完全兼容原有的 `getLocation()` 方法
- 新功能为可选增强，不影响现有代码
- 支持所有uni-app平台

## 回退方案
如果新功能出现问题，可以通过以下方式回退：
1. 注释掉 `_handleLocationPermissionError()` 调用
2. 恢复原有的简单错误处理逻辑
3. 保留原有的缓存和存储功能

## 测试建议
1. 在不同设备上测试权限弹窗显示
2. 测试系统设置跳转功能
3. 验证权限授权后的定位获取
4. 测试网络异常情况下的降级处理
