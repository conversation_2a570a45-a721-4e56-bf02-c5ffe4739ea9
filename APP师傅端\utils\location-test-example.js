/**
 * 定位管理器使用示例
 * 演示如何使用新的定位权限功能
 */

import locationManager from './location-manager.js';

// 示例1: 检查定位权限（会显示弹窗引导）
export async function checkLocationPermissionExample() {
  console.log('=== 检查定位权限示例 ===');
  
  const hasPermission = await locationManager.checkLocationPermission();
  
  if (hasPermission) {
    console.log('用户已授权定位权限');
    // 可以继续获取定位信息
    try {
      const location = await locationManager.getLocation();
      console.log('定位获取成功:', location);
    } catch (error) {
      console.error('定位获取失败:', error);
    }
  } else {
    console.log('用户未授权定位权限或定位功能未开启');
  }
}

// 示例2: 静默检查定位权限（不显示弹窗）
export async function silentCheckLocationPermissionExample() {
  console.log('=== 静默检查定位权限示例 ===');
  
  const hasPermission = await locationManager.checkLocationPermission({ showModal: false });
  
  if (hasPermission) {
    console.log('定位权限可用');
  } else {
    console.log('定位权限不可用，需要手动处理');
    // 可以在适当的时机手动显示权限设置弹窗
    // locationManager.showLocationPermissionModal();
  }
}

// 示例3: 手动触发权限设置弹窗
export function showLocationSettingsExample() {
  console.log('=== 手动显示定位权限设置弹窗 ===');
  locationManager.showLocationPermissionModal();
}

// 示例4: 在页面中使用（Vue组件示例）
export const vueComponentExample = {
  methods: {
    async handleLocationRequest() {
      try {
        // 先检查权限
        const hasPermission = await locationManager.checkLocationPermission();
        
        if (hasPermission) {
          // 有权限，获取定位
          const location = await locationManager.getLocation({ silent: false });
          console.log('定位成功:', location);
          
          // 使用定位信息
          this.handleLocationSuccess(location);
        } else {
          // 没有权限，用户会看到权限设置弹窗
          console.log('等待用户设置权限...');
        }
      } catch (error) {
        console.error('定位处理失败:', error);
        this.handleLocationError(error);
      }
    },
    
    handleLocationSuccess(location) {
      // 处理定位成功的逻辑
      console.log('处理定位成功:', location);
    },
    
    handleLocationError(error) {
      // 处理定位失败的逻辑
      console.log('处理定位失败:', error);
    }
  }
};

// 示例5: 在应用启动时检查定位权限
export async function appStartupLocationCheck() {
  console.log('=== 应用启动定位权限检查 ===');
  
  // 静默检查，不立即显示弹窗
  const hasPermission = await locationManager.checkLocationPermission({ showModal: false });
  
  if (hasPermission) {
    // 有权限，可以在后台获取定位
    try {
      await locationManager.getLocation({ silent: true });
      console.log('后台定位获取成功');
    } catch (error) {
      console.log('后台定位获取失败，但不影响应用启动');
    }
  } else {
    console.log('定位权限不可用，将在需要时提示用户');
  }
}
